{"schemaVersion": 1, "id": "waypoints", "version": "${version}", "name": "Waypoints", "description": "This is an example description! Tell everyone what your mod is about!", "authors": ["Me!"], "contact": {"homepage": "https://fabricmc.net/", "sources": "https://github.com/FabricMC/fabric-example-mod"}, "license": "CC0-1.0", "icon": "assets/waypoints/icon.png", "environment": "*", "entrypoints": {"main": ["ru.fix85.Waypoints"], "client": ["ru.fix85.WaypointsClient"]}, "mixins": ["waypoints.mixins.json", {"config": "waypoints.client.mixins.json", "environment": "client"}], "depends": {"fabricloader": ">=0.16.14", "minecraft": "~1.21", "java": ">=21", "fabric-api": "*"}, "suggests": {"another-mod": "*"}}